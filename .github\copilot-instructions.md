# Copilot Instructions for SDN Project

## Project Overview
This project implements a proxy system with a server-client architecture for dynamic URL and port mapping, supporting HTTP(S) and raw TCP forwarding. It is designed for secure, efficient, and observable cross-network service exposure.

## Architecture
- **Server (`server/`)**: Handles HTTP management, TCP proxying, tunnel/connection management, monitoring, and persistence. Key entry: `server/main.go`.
- **Client (`client/`)**: Registers with the server, manages URL/port mappings, establishes data tunnels, and handles reconnection logic. Key entry: `client/main.go`.
- **Relay (`relay/`)**: Contains relay logic and shared components.
- **Common/Domain/Entity/Service**: Shared data structures and business logic, e.g., `ConnMessage`, `URLProxyMessage`, `URLMapping`, `SafeConn`.
- **Monitoring**: Traffic and request statistics are tracked per client and baseURL.

## Key Workflows
- **Registration**: Client registers via `/register` (HTTP Upgrade to TCP). Server maintains a persistent control tunnel (SafeConn).
- **URL Mapping**: Clients register/unregister URL mappings via `/url/register` and `/url/unregister`. Server routes incoming HTTP(S) requests to the correct client based on baseURL.
- **Port Proxy**: Clients request public port allocation via `/allocate`. Server listens and forwards traffic to the client.
- **Data Tunnel**: For each proxied request, a data tunnel is established via `/tcp/data` (TCP Upgrade), binding server and client connections for raw byte forwarding.
- **Monitoring**: All HTTP requests are logged and counted for observability.

## Developer Workflows
- **Build**: Use `build.sh` (Linux) or build Go binaries directly. Example: `go build ./server`.
- **Test**: Use scripts in `test/` (e.g., `run_tests.sh`, `run_tests_simple.sh`) or run `go test ./...` in relevant modules.
- **Debug**: Focus on connection lifecycle, tunnel management, and mapping logic. Key files: `server/application/event/`, `server/domain/entity/`, `server/service/`.

## Project-Specific Conventions
- **Control/Data Plane Split**: Control messages (JSON) use persistent TCP (SafeConn); data tunnels use separate TCP connections for raw byte streaming.
- **Connection Lifecycle**: Always read to EOF before closing sockets to avoid data loss. Reconnection is blocking and retried every 30s on failure.
- **Mapping Management**: All mappings (URL/port) are cached and periodically cleaned up. On startup, mappings are restored from DB.
- **Monitoring**: Use `monitor.GetGlobalMonitor()` for request stats. Register monitoring items on mapping creation.
- **Error Handling**: 404 for unmapped, 503 for offline, 500 for internal errors. See `server/handlers/`.

## Integration & Dependencies
- **External**: Uses Go modules (see `go.mod` in each component). No external DB required for basic operation; uses local persistence.
- **Communication**: All client-server communication is over HTTP(S) with TCP upgrades for tunnels.

## Examples
- Register a client: `GET /register?id=clientUUID`
- Register a URL mapping: `POST /url/register` with JSON body
- Allocate a port: `GET /allocate?id=clientUUID&port=xxxx`
- Establish a data tunnel: `GET /tcp/data?client_uuid=...&conn_id=...`

## Key Files & Directories
- `server/main.go`, `client/main.go`: Entrypoints
- `server/application/event/`, `server/service/`, `server/domain/entity/`: Core logic
- `monitor/`: Traffic monitoring
- `test/`: Test scripts and integration tests
- `util/`: Shared utilities and config

## Patterns & Gotchas
- Always remove tunnels from maps on exit to avoid leaks.
- Use blocking reconnection logic for client control tunnels.
- All protocol messages are JSON; binary data is Base64-encoded if needed.
- Prefer direct TCP forwarding for large/streaming payloads.

---
For more details, see `README.md` and `docs/` for design and protocol documentation.
